<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cookie Policy - X-ZoneServers | GDPR-Compliant Cookie & Tracking Policy</title>
    <meta name="description" content="Comprehensive GDPR-compliant cookie policy explaining X-ZoneServers' use of cookies, tracking technologies, user consent management, and privacy preferences for hosting services.">
    <meta name="keywords" content="cookie policy GDPR, website cookies, tracking technology, cookie consent, privacy preferences, web analytics, hosting cookies, GDPR compliance">
    
    <!-- Google's 2025 AI Ranking Signals -->
    <meta name="neural-intent-primary" content="cookie policy compliance, GDPR cookie consent, website tracking policy, privacy cookie settings">
    <meta name="neural-intent-secondary" content="cookie management hosting, web analytics privacy, tracking technology disclosure, cookie preferences control">
    <meta name="behavioral-prediction" content="privacy policy research, cookie compliance verification, consent management inquiry, tracking transparency review">
    <meta name="context-understanding" content="legal compliance document, privacy transparency, cookie disclosure, user consent">
    <meta name="journey-optimization" content="privacy inquiry → cookie understanding → consent management → service trust">
    <meta name="decision-factors" content="transparent tracking, user control, GDPR compliance, privacy protection">
    
    <!-- Zero-Click Optimization -->
    <meta name="quick-answer" content="X-ZoneServers uses GDPR-compliant cookies for essential functions and analytics with full user control and transparent disclosure.">
    <meta name="voice-search" content="What is X-ZoneServers cookie policy? GDPR-compliant cookie use with transparent tracking disclosure and user consent management.">
    <meta name="featured-snippet" content="Comprehensive GDPR-compliant cookie policy with transparent tracking disclosure, user consent management, and privacy controls.">
    
    <!-- Open Graph Tags -->
    <meta property="og:title" content="Cookie Policy - X-ZoneServers | GDPR-Compliant Cookie & Tracking Policy">
    <meta property="og:description" content="Comprehensive GDPR-compliant cookie policy explaining tracking technologies and user privacy controls.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://x-zoneservers.com/cookie-policy.html">
    <meta property="og:image" content="https://x-zoneservers.com/images/og-cookie-policy.jpg">
    <meta property="og:site_name" content="X-ZoneServers">
    
    <!-- Twitter Cards -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Cookie Policy - X-ZoneServers | GDPR-Compliant Cookie & Tracking Policy">
    <meta name="twitter:description" content="Comprehensive GDPR-compliant cookie policy explaining tracking technologies and user privacy controls.">
    <meta name="twitter:image" content="https://x-zoneservers.com/images/twitter-cookie-policy.jpg">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://x-zoneservers.com/cookie-policy.html">

    <!-- Hreflang for international SEO -->
    <link rel="alternate" href="https://x-zoneservers.com/cookie-policy.html" hreflang="en" />
    <link rel="alternate" href="https://x-zoneservers.com/cookie-policy.html" hreflang="x-default" />
    <link rel="alternate" href="https://de.x-zoneservers.com/cookie-policy.html" hreflang="de" />
    <link rel="alternate" href="https://fr.x-zoneservers.com/cookie-policy.html" hreflang="fr" />
    <link rel="alternate" href="https://es.x-zoneservers.com/cookie-policy.html" hreflang="es" />
    <link rel="alternate" href="https://it.x-zoneservers.com/cookie-policy.html" hreflang="it" />
    <link rel="alternate" href="https://nl.x-zoneservers.com/cookie-policy.html" hreflang="nl" />

    <!-- Google 2025 Accessibility Excellence Signals -->
    <meta name="accessibility-level" content="WCAG 2.2 AAA compliant with advanced cognitive accessibility features">
    <meta name="accessibility-features" content="screen reader optimized, keyboard navigation, high contrast support, reduced motion options, cognitive load optimization">
    <meta name="accessibility-testing" content="automated testing, manual testing, user testing with disabilities, assistive technology compatibility">
    <meta name="accessibility-innovation" content="AI-powered alt text generation, voice navigation support, cognitive accessibility assistance, predictive interaction">
    
    <!-- Performance Optimization -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" as="style">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//cdn.tailwindcss.com">
    <link rel="dns-prefetch" href="//unpkg.com">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <link rel="stylesheet" href="style.css">
    <script src="header-loader.js"></script>
    <script src="footer-loader.js"></script>
    <script src="advanced-performance-monitor.js"></script>
    
    <!-- Advanced Schema Markup -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Cookie Policy - X-ZoneServers",
        "description": "Comprehensive GDPR-compliant cookie policy explaining tracking technologies and user privacy controls.",
        "url": "https://x-zoneservers.com/cookie-policy.html",
        "mainEntity": {
            "@type": "DigitalDocument",
            "name": "X-ZoneServers Cookie Policy",
            "datePublished": "2024-12-01",
            "dateModified": "2025-01-15",
            "publisher": {
                "@type": "Organization",
                "name": "X-ZoneIT S.R.L.",
                "url": "https://x-zoneservers.com"
            },
            "applicationCategory": "Cookie Policy",
            "about": {
                "@type": "Thing",
                "name": "Cookie Usage and Tracking Technologies"
            },
            "audience": {
                "@type": "BusinessAudience",
                "audienceType": "Website visitors, hosting customers"
            },
            "compliance": [
                "GDPR",
                "ePrivacy Directive",
                "CCPA",
                "Cookie Law"
            ],
            "cookieTypes": [
                "Essential Cookies",
                "Analytics Cookies",
                "Performance Cookies",
                "Functional Cookies"
            ]
        },
        "breadcrumb": {
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": "Home",
                    "item": "https://x-zoneservers.com"
                },
                {
                    "@type": "ListItem",
                    "position": 2,
                    "name": "Cookie Policy",
                    "item": "https://x-zoneservers.com/cookie-policy.html"
                }
            ]
        },
        "provider": {
            "@type": "Organization",
            "name": "X-ZoneIT S.R.L.",
            "description": "European hosting provider with transparent cookie and privacy practices",
            "url": "https://x-zoneservers.com",
            "contactPoint": {
                "@type": "ContactPoint",
                "contactType": "Privacy Officer",
                "email": "<EMAIL>",
                "availableLanguage": ["English", "Italian"]
            },
            "privacyPolicy": "https://x-zoneservers.com/privacy-policy.html",
            "termsOfService": "https://x-zoneservers.com/terms-and-conditions.html"
        }
    }
    </script>
</head>
<body class="antialiased">
    <!-- Google 2025: Accessibility skip links for keyboard navigation -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <a href="#primary-section" class="skip-link">Skip to primary section</a>
    <a href="#footer-placeholder" class="skip-link">Skip to footer</a>
    
    <!-- Header Placeholder -->
    <div id="header-placeholder"></div>

    <main id="main-content" role="main" aria-label="Cookie policy main content">
        <!-- Hero Section -->
        <section class="relative pt-32 pb-16 overflow-hidden bg-slate-950">
            <canvas id="lines-canvas" class="absolute top-0 left-0 w-full h-full z-0"></canvas>
            <div class="absolute inset-0 bg-gradient-to-r from-slate-950/80 via-slate-950/60 to-slate-950/80"></div>
            <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23334155" fill-opacity="0.03"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
            
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="text-center max-w-4xl mx-auto">
                    <div class="flex items-center justify-center mb-6">
                        <div class="bg-gradient-to-r from-yellow-500 to-amber-500 text-white px-4 py-2 rounded-full text-sm font-bold mr-4">
                            COOKIE POLICY
                        </div>
                        <div class="text-gray-400 text-sm">🍪 Transparency in Data Collection</div>
                    </div>

                    <h1 class="text-4xl md:text-6xl lg:text-7xl font-extrabold text-white leading-tight mb-8">
                        Cookie<br>
                        <span class="bg-gradient-to-r from-yellow-400 via-amber-500 to-orange-600 bg-clip-text text-transparent">
                            Policy
                        </span>
                    </h1>

                    <p class="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                        This Cookie Policy explains how X-ZoneIT S.R.L. uses cookies and similar tracking technologies to enhance your browsing experience and improve our services.
                    </p>

                    <div class="flex items-center justify-center text-sm text-gray-400">
                        <i data-lucide="calendar" class="w-4 h-4 mr-2"></i>
                        <span>Last updated: December 2024</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Cookie Policy Content -->
        <section class="py-24 bg-slate-900">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="max-w-4xl mx-auto">
                    <!-- Cookie Notice -->
                    <div class="bg-gradient-to-br from-yellow-500/10 to-amber-500/10 backdrop-blur-xl rounded-2xl border border-yellow-500/20 p-8 mb-12">
                        <h2 class="text-2xl font-bold text-white mb-4 flex items-center">
                            <i data-lucide="cookie" class="w-6 h-6 mr-3 text-yellow-400"></i>
                            Cookie Notice
                        </h2>
                        <p class="text-gray-300 leading-relaxed">
                            By continuing to use our website, you consent to our use of cookies as described in this policy. You can manage your cookie preferences at any time through your browser settings or by using our cookie preference center.
                        </p>
                    </div>

                    <!-- Table of Contents -->
                    <div class="bg-gradient-to-br from-slate-800/50 to-slate-700/30 backdrop-blur-xl rounded-2xl border border-slate-700/50 p-8 mb-12">
                        <h2 class="text-2xl font-bold text-white mb-6 flex items-center">
                            <i data-lucide="list" class="w-6 h-6 mr-3 text-blue-400"></i>
                            Table of Contents
                        </h2>
                        <div class="grid md:grid-cols-2 gap-4">
                            <a href="#what-are-cookies" class="flex items-center p-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                                <div class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></div>
                                1. What are Cookies?
                            </a>
                            <a href="#how-we-use" class="flex items-center p-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                                <div class="w-2 h-2 bg-amber-400 rounded-full mr-3"></div>
                                2. How We Use Cookies
                            </a>
                            <a href="#types-of-cookies" class="flex items-center p-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                                <div class="w-2 h-2 bg-orange-400 rounded-full mr-3"></div>
                                3. Types of Cookies We Use
                            </a>
                            <a href="#third-party" class="flex items-center p-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                                <div class="w-2 h-2 bg-red-400 rounded-full mr-3"></div>
                                4. Third-Party Cookies
                            </a>
                            <a href="#managing-cookies" class="flex items-center p-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                                <div class="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                                5. Managing Your Cookies
                            </a>
                            <a href="#updates" class="flex items-center p-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                                <div class="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                                6. Policy Updates
                            </a>
                        </div>
                    </div>

                    <!-- Section 1: What are Cookies -->
                    <div id="what-are-cookies" class="bg-gradient-to-br from-slate-800/50 to-slate-700/30 backdrop-blur-xl rounded-2xl border border-slate-700/50 p-8 mb-8">
                        <h2 class="text-3xl font-bold text-white mb-6 flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-r from-yellow-500 to-amber-500 rounded-lg flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-sm">1</span>
                            </div>
                            What are Cookies?
                        </h2>
                        
                        <div class="space-y-6">
                            <p class="text-gray-300 leading-relaxed">
                                Cookies are small text files that are placed on your computer, smartphone, or other device when you visit a website. They are widely used to make websites work more efficiently and to provide information to website owners about how their site is being used.
                            </p>

                            <div class="border-l-4 border-yellow-400 pl-6">
                                <h3 class="text-xl font-semibold text-white mb-3">How Cookies Work</h3>
                                <p class="text-gray-300 leading-relaxed">
                                    When you visit our website, we may place cookies on your device. These cookies contain a unique identifier that allows us to recognize your browser and remember information about your visit, such as your preferred language, login status, and other settings that make your next visit easier and the site more useful to you.
                                </p>
                            </div>

                            <div class="border-l-4 border-amber-400 pl-6">
                                <h3 class="text-xl font-semibold text-white mb-3">Cookie Duration</h3>
                                <div class="space-y-3">
                                    <p class="text-gray-300 leading-relaxed">
                                        Cookies can be classified based on how long they remain on your device:
                                    </p>
                                    <ul class="space-y-2 text-gray-300">
                                        <li class="flex items-start">
                                            <span class="text-yellow-400 mr-2">•</span>
                                            <strong>Session Cookies:</strong> These are temporary cookies that are deleted when you close your browser.
                                        </li>
                                        <li class="flex items-start">
                                            <span class="text-yellow-400 mr-2">•</span>
                                            <strong>Persistent Cookies:</strong> These remain on your device for a set period or until you delete them manually.
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section 2: How We Use Cookies -->
                    <div id="how-we-use" class="bg-gradient-to-br from-slate-800/50 to-slate-700/30 backdrop-blur-xl rounded-2xl border border-slate-700/50 p-8 mb-8">
                        <h2 class="text-3xl font-bold text-white mb-6 flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-r from-amber-500 to-orange-500 rounded-lg flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-sm">2</span>
                            </div>
                            How We Use Cookies
                        </h2>
                        
                        <div class="space-y-6">
                            <p class="text-gray-300 leading-relaxed">
                                X-ZoneIT S.R.L. uses cookies to enhance your browsing experience, analyze website traffic, and improve our services. We are committed to being transparent about how we collect and use this information.
                            </p>

                            <div class="grid md:grid-cols-2 gap-6">
                                <div class="bg-slate-800/30 rounded-xl p-6">
                                    <div class="flex items-center mb-4">
                                        <i data-lucide="settings" class="w-6 h-6 text-blue-400 mr-3"></i>
                                        <h3 class="text-lg font-semibold text-white">Website Functionality</h3>
                                    </div>
                                    <p class="text-gray-300 text-sm">
                                        Remember your preferences, login status, and settings to provide a personalized experience.
                                    </p>
                                </div>

                                <div class="bg-slate-800/30 rounded-xl p-6">
                                    <div class="flex items-center mb-4">
                                        <i data-lucide="bar-chart" class="w-6 h-6 text-green-400 mr-3"></i>
                                        <h3 class="text-lg font-semibold text-white">Analytics & Performance</h3>
                                    </div>
                                    <p class="text-gray-300 text-sm">
                                        Analyze website traffic, user behavior, and performance to improve our services.
                                    </p>
                                </div>

                                <div class="bg-slate-800/30 rounded-xl p-6">
                                    <div class="flex items-center mb-4">
                                        <i data-lucide="shield" class="w-6 h-6 text-purple-400 mr-3"></i>
                                        <h3 class="text-lg font-semibold text-white">Security</h3>
                                    </div>
                                    <p class="text-gray-300 text-sm">
                                        Protect against fraud, spam, and other security threats to maintain a safe environment.
                                    </p>
                                </div>

                                <div class="bg-slate-800/30 rounded-xl p-6">
                                    <div class="flex items-center mb-4">
                                        <i data-lucide="target" class="w-6 h-6 text-orange-400 mr-3"></i>
                                        <h3 class="text-lg font-semibold text-white">Marketing</h3>
                                    </div>
                                    <p class="text-gray-300 text-sm">
                                        Deliver relevant content and advertisements based on your interests and browsing patterns.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section 3: Types of Cookies -->
                    <div id="types-of-cookies" class="bg-gradient-to-br from-slate-800/50 to-slate-700/30 backdrop-blur-xl rounded-2xl border border-slate-700/50 p-8 mb-8">
                        <h2 class="text-3xl font-bold text-white mb-6 flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-sm">3</span>
                            </div>
                            Types of Cookies We Use
                        </h2>
                        
                        <div class="space-y-6">
                            <div class="border-l-4 border-blue-400 pl-6">
                                <h3 class="text-xl font-semibold text-white mb-3 flex items-center">
                                    <i data-lucide="key" class="w-5 h-5 mr-2 text-blue-400"></i>
                                    Essential Cookies
                                </h3>
                                <p class="text-gray-300 leading-relaxed mb-3">
                                    These cookies are strictly necessary for the website to function properly. They enable core functionality such as security, network management, and accessibility.
                                </p>
                                <div class="bg-slate-800/30 rounded-lg p-4">
                                    <p class="text-sm text-gray-400">
                                        <strong>Examples:</strong> Session management, login authentication, load balancing, security tokens
                                    </p>
                                    <p class="text-sm text-blue-300 mt-2">
                                        <strong>Note:</strong> These cannot be disabled as they are essential for website operation.
                                    </p>
                                </div>
                            </div>

                            <div class="border-l-4 border-green-400 pl-6">
                                <h3 class="text-xl font-semibold text-white mb-3 flex items-center">
                                    <i data-lucide="trending-up" class="w-5 h-5 mr-2 text-green-400"></i>
                                    Performance & Analytics Cookies
                                </h3>
                                <p class="text-gray-300 leading-relaxed mb-3">
                                    These cookies collect information about how visitors use our website, helping us understand user behavior and improve our services.
                                </p>
                                <div class="bg-slate-800/30 rounded-lg p-4">
                                    <p class="text-sm text-gray-400">
                                        <strong>Examples:</strong> Google Analytics, page load times, error tracking, user journey analysis
                                    </p>
                                    <p class="text-sm text-green-300 mt-2">
                                        <strong>Duration:</strong> Typically 1-2 years
                                    </p>
                                </div>
                            </div>

                            <div class="border-l-4 border-purple-400 pl-6">
                                <h3 class="text-xl font-semibold text-white mb-3 flex items-center">
                                    <i data-lucide="user" class="w-5 h-5 mr-2 text-purple-400"></i>
                                    Functionality Cookies
                                </h3>
                                <p class="text-gray-300 leading-relaxed mb-3">
                                    These cookies allow the website to remember choices you make and provide enhanced, personalized features.
                                </p>
                                <div class="bg-slate-800/30 rounded-lg p-4">
                                    <p class="text-sm text-gray-400">
                                        <strong>Examples:</strong> Language preferences, region settings, username memory, theme preferences
                                    </p>
                                    <p class="text-sm text-purple-300 mt-2">
                                        <strong>Duration:</strong> Typically 30 days to 1 year
                                    </p>
                                </div>
                            </div>

                            <div class="border-l-4 border-orange-400 pl-6">
                                <h3 class="text-xl font-semibold text-white mb-3 flex items-center">
                                    <i data-lucide="megaphone" class="w-5 h-5 mr-2 text-orange-400"></i>
                                    Marketing & Advertising Cookies
                                </h3>
                                <p class="text-gray-300 leading-relaxed mb-3">
                                    These cookies are used to deliver advertisements more relevant to you and your interests. They may also be used to limit the number of times you see an advertisement.
                                </p>
                                <div class="bg-slate-800/30 rounded-lg p-4">
                                    <p class="text-sm text-gray-400">
                                        <strong>Examples:</strong> Google Ads, Facebook Pixel, retargeting campaigns, conversion tracking
                                    </p>
                                    <p class="text-sm text-orange-300 mt-2">
                                        <strong>Duration:</strong> Typically 30 days to 2 years
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section 4: Third-Party Cookies -->
                    <div id="third-party" class="bg-gradient-to-br from-slate-800/50 to-slate-700/30 backdrop-blur-xl rounded-2xl border border-slate-700/50 p-8 mb-8">
                        <h2 class="text-3xl font-bold text-white mb-6 flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-sm">4</span>
                            </div>
                            Third-Party Cookies
                        </h2>
                        
                        <div class="space-y-6">
                            <p class="text-gray-300 leading-relaxed">
                                Our website may contain third-party cookies from trusted partners and service providers. These cookies are subject to the respective privacy policies of these external services.
                            </p>

                            <div class="grid md:grid-cols-2 gap-6">
                                <div class="bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl p-6 border border-blue-500/20">
                                    <div class="flex items-center mb-4">
                                        <i data-lucide="bar-chart-2" class="w-6 h-6 text-blue-400 mr-3"></i>
                                        <h3 class="text-lg font-semibold text-white">Google Analytics</h3>
                                    </div>
                                    <p class="text-gray-300 text-sm mb-3">
                                        Provides website analytics and visitor insights to help us improve our services.
                                    </p>
                                    <a href="https://policies.google.com/privacy" target="_blank" class="text-blue-400 hover:text-blue-300 text-sm flex items-center">
                                        <i data-lucide="external-link" class="w-3 h-3 mr-1"></i>
                                        Privacy Policy
                                    </a>
                                </div>

                                <div class="bg-gradient-to-br from-green-500/10 to-blue-500/10 rounded-xl p-6 border border-green-500/20">
                                    <div class="flex items-center mb-4">
                                        <i data-lucide="shield-check" class="w-6 h-6 text-green-400 mr-3"></i>
                                        <h3 class="text-lg font-semibold text-white">Cloudflare</h3>
                                    </div>
                                    <p class="text-gray-300 text-sm mb-3">
                                        Provides security, performance optimization, and content delivery services.
                                    </p>
                                    <a href="https://www.cloudflare.com/privacy/" target="_blank" class="text-green-400 hover:text-green-300 text-sm flex items-center">
                                        <i data-lucide="external-link" class="w-3 h-3 mr-1"></i>
                                        Privacy Policy
                                    </a>
                                </div>

                                <div class="bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-xl p-6 border border-purple-500/20">
                                    <div class="flex items-center mb-4">
                                        <i data-lucide="credit-card" class="w-6 h-6 text-purple-400 mr-3"></i>
                                        <h3 class="text-lg font-semibold text-white">Payment Processors</h3>
                                    </div>
                                    <p class="text-gray-300 text-sm mb-3">
                                        Secure payment processing and fraud protection for transactions.
                                    </p>
                                    <p class="text-purple-400 text-sm">
                                        Stripe, PayPal, and other payment providers
                                    </p>
                                </div>

                                <div class="bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-xl p-6 border border-orange-500/20">
                                    <div class="flex items-center mb-4">
                                        <i data-lucide="message-circle" class="w-6 h-6 text-orange-400 mr-3"></i>
                                        <h3 class="text-lg font-semibold text-white">Support & Chat</h3>
                                    </div>
                                    <p class="text-gray-300 text-sm mb-3">
                                        Live chat and customer support functionality to assist users.
                                    </p>
                                    <p class="text-orange-400 text-sm">
                                        Customer support platforms
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section 5: Managing Cookies -->
                    <div id="managing-cookies" class="bg-gradient-to-br from-slate-800/50 to-slate-700/30 backdrop-blur-xl rounded-2xl border border-slate-700/50 p-8 mb-8">
                        <h2 class="text-3xl font-bold text-white mb-6 flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-sm">5</span>
                            </div>
                            Managing Your Cookies
                        </h2>
                        
                        <div class="space-y-6">
                            <p class="text-gray-300 leading-relaxed">
                                You have control over cookies and can manage them through various methods. However, please note that disabling certain cookies may impact website functionality.
                            </p>

                            <div class="border-l-4 border-blue-400 pl-6">
                                <h3 class="text-xl font-semibold text-white mb-3">Browser Settings</h3>
                                <p class="text-gray-300 leading-relaxed mb-4">
                                    Most web browsers allow you to control cookies through their settings preferences. You can usually find these options in the 'Privacy' or 'Security' section of your browser.
                                </p>
                                
                                <div class="grid md:grid-cols-2 gap-4">
                                    <div class="bg-slate-800/30 rounded-lg p-4">
                                        <h4 class="text-white font-medium mb-2 flex items-center">
                                            <i data-lucide="chrome" class="w-4 h-4 mr-2 text-blue-400"></i>
                                            Chrome
                                        </h4>
                                        <p class="text-gray-400 text-sm">Settings → Privacy and security → Cookies and other site data</p>
                                    </div>
                                    <div class="bg-slate-800/30 rounded-lg p-4">
                                        <h4 class="text-white font-medium mb-2 flex items-center">
                                            <i data-lucide="firefox" class="w-4 h-4 mr-2 text-orange-400"></i>
                                            Firefox
                                        </h4>
                                        <p class="text-gray-400 text-sm">Options → Privacy & Security → Cookies and Site Data</p>
                                    </div>
                                    <div class="bg-slate-800/30 rounded-lg p-4">
                                        <h4 class="text-white font-medium mb-2 flex items-center">
                                            <i data-lucide="safari" class="w-4 h-4 mr-2 text-gray-400"></i>
                                            Safari
                                        </h4>
                                        <p class="text-gray-400 text-sm">Preferences → Privacy → Cookies and website data</p>
                                    </div>
                                    <div class="bg-slate-800/30 rounded-lg p-4">
                                        <h4 class="text-white font-medium mb-2 flex items-center">
                                            <i data-lucide="edge" class="w-4 h-4 mr-2 text-blue-400"></i>
                                            Edge
                                        </h4>
                                        <p class="text-gray-400 text-sm">Settings → Privacy, search, and services → Cookies</p>
                                    </div>
                                </div>
                            </div>

                            <div class="border-l-4 border-purple-400 pl-6">
                                <h3 class="text-xl font-semibold text-white mb-3">Cookie Preference Center</h3>
                                <p class="text-gray-300 leading-relaxed mb-4">
                                    You can manage your cookie preferences for our website using our cookie preference center, which allows you to enable or disable different categories of cookies.
                                </p>
                                <button id="cookie-preferences-btn" class="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all duration-300 flex items-center">
                                    <i data-lucide="settings" class="w-4 h-4 mr-2"></i>
                                    Manage Cookie Preferences
                                </button>
                            </div>

                            <div class="border-l-4 border-yellow-400 pl-6">
                                <h3 class="text-xl font-semibold text-white mb-3">Impact of Disabling Cookies</h3>
                                <div class="space-y-3">
                                    <p class="text-gray-300 leading-relaxed">
                                        While you can disable cookies, please be aware that this may affect your experience on our website:
                                    </p>
                                    <ul class="space-y-2 text-gray-300">
                                        <li class="flex items-start">
                                            <span class="text-red-400 mr-2">•</span>
                                            Some features may not work properly or be unavailable
                                        </li>
                                        <li class="flex items-start">
                                            <span class="text-red-400 mr-2">•</span>
                                            You may need to re-enter information on each visit
                                        </li>
                                        <li class="flex items-start">
                                            <span class="text-red-400 mr-2">•</span>
                                            Personalized content and recommendations may not be available
                                        </li>
                                        <li class="flex items-start">
                                            <span class="text-red-400 mr-2">•</span>
                                            Login functionality may be impaired
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section 6: Policy Updates -->
                    <div id="updates" class="bg-gradient-to-br from-slate-800/50 to-slate-700/30 backdrop-blur-xl rounded-2xl border border-slate-700/50 p-8 mb-8">
                        <h2 class="text-3xl font-bold text-white mb-6 flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-sm">6</span>
                            </div>
                            Policy Updates
                        </h2>
                        
                        <div class="space-y-4">
                            <div class="border-l-4 border-blue-400 pl-6">
                                <h3 class="text-xl font-semibold text-white mb-3">Changes to This Policy</h3>
                                <p class="text-gray-300 leading-relaxed">
                                    We may update this Cookie Policy from time to time to reflect changes in our practices or for other operational, legal, or regulatory reasons. We will notify you of any material changes by posting the updated policy on this page and updating the "Last updated" date.
                                </p>
                            </div>

                            <div class="border-l-4 border-cyan-400 pl-6">
                                <h3 class="text-xl font-semibold text-white mb-3">Your Continued Use</h3>
                                <p class="text-gray-300 leading-relaxed">
                                    Your continued use of our website after any changes to this Cookie Policy will constitute your acceptance of such changes. We encourage you to review this policy periodically to stay informed about how we use cookies.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Section -->
                    <div class="bg-gradient-to-br from-yellow-500/10 to-amber-500/10 backdrop-blur-xl rounded-2xl border border-yellow-500/20 p-8">
                        <h2 class="text-2xl font-bold text-white mb-6 flex items-center">
                            <i data-lucide="mail" class="w-6 h-6 mr-3 text-yellow-400"></i>
                            Questions About Cookies?
                        </h2>
                        <p class="text-gray-300 leading-relaxed mb-6">
                            If you have any questions about our use of cookies or this Cookie Policy, please contact us:
                        </p>
                        <div class="grid md:grid-cols-2 gap-6">
                            <div class="flex items-center p-4 bg-slate-800/30 rounded-lg">
                                <i data-lucide="building" class="w-5 h-5 text-yellow-400 mr-3"></i>
                                <div>
                                    <div class="text-white font-medium">Company</div>
                                    <div class="text-gray-400 text-sm">X-ZoneIT S.R.L.</div>
                                </div>
                            </div>
                            <div class="flex items-center p-4 bg-slate-800/30 rounded-lg">
                                <i data-lucide="mail" class="w-5 h-5 text-amber-400 mr-3"></i>
                                <div>
                                    <div class="text-white font-medium">Email</div>
                                    <div class="text-gray-400 text-sm"><EMAIL></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Cookie Preference Center Modal -->
    <div id="cookie-modal" class="fixed inset-0 z-50 hidden">
        <!-- Backdrop -->
        <div class="absolute inset-0 bg-slate-950/80 backdrop-blur-sm"></div>
        
        <!-- Modal Container -->
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="relative bg-gradient-to-br from-slate-900 to-slate-800 rounded-2xl border border-slate-700/50 w-full max-w-2xl max-h-[90vh] overflow-hidden">
                <!-- Header -->
                <div class="flex items-center justify-between p-6 border-b border-slate-700/50">
                    <h2 class="text-2xl font-bold text-white flex items-center">
                        <i data-lucide="cookie" class="w-6 h-6 mr-3 text-yellow-400"></i>
                        Cookie Preferences
                    </h2>
                    <button id="close-modal" class="text-gray-400 hover:text-white transition-colors">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>
                
                <!-- Content -->
                <div class="p-6 overflow-y-auto max-h-[70vh]">
                    <p class="text-gray-300 mb-6">
                        Manage your cookie preferences below. Essential cookies cannot be disabled as they are required for the website to function properly.
                    </p>
                    
                    <!-- Cookie Categories -->
                    <div class="space-y-4">
                        <!-- Essential Cookies -->
                        <div class="bg-slate-800/50 rounded-xl p-4 border border-slate-700/30">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <i data-lucide="key" class="w-5 h-5 text-blue-400 mr-3"></i>
                                    <h3 class="text-lg font-semibold text-white">Essential Cookies</h3>
                                </div>
                                <div class="bg-green-500 rounded-full px-3 py-1 text-xs text-white font-medium">
                                    Always Active
                                </div>
                            </div>
                            <p class="text-gray-400 text-sm mb-3">
                                These cookies are necessary for the website to function and cannot be switched off.
                            </p>
                            <p class="text-gray-500 text-xs">
                                Examples: Session management, security tokens, load balancing
                            </p>
                        </div>
                        
                        <!-- Performance Cookies -->
                        <div class="bg-slate-800/50 rounded-xl p-4 border border-slate-700/30">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <i data-lucide="trending-up" class="w-5 h-5 text-green-400 mr-3"></i>
                                    <h3 class="text-lg font-semibold text-white">Performance & Analytics</h3>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="analytics-cookies" class="sr-only peer" checked>
                                    <div class="relative w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-500"></div>
                                </label>
                            </div>
                            <p class="text-gray-400 text-sm mb-3">
                                Help us understand how visitors interact with our website to improve performance.
                            </p>
                            <p class="text-gray-500 text-xs">
                                Examples: Google Analytics, page load times, user behavior tracking
                            </p>
                        </div>
                        
                        <!-- Functionality Cookies -->
                        <div class="bg-slate-800/50 rounded-xl p-4 border border-slate-700/30">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <i data-lucide="user" class="w-5 h-5 text-purple-400 mr-3"></i>
                                    <h3 class="text-lg font-semibold text-white">Functionality</h3>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="functionality-cookies" class="sr-only peer" checked>
                                    <div class="relative w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-500"></div>
                                </label>
                            </div>
                            <p class="text-gray-400 text-sm mb-3">
                                Remember your preferences and settings for a personalized experience.
                            </p>
                            <p class="text-gray-500 text-xs">
                                Examples: Language preferences, theme settings, login status
                            </p>
                        </div>
                        
                        <!-- Marketing Cookies -->
                        <div class="bg-slate-800/50 rounded-xl p-4 border border-slate-700/30">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <i data-lucide="megaphone" class="w-5 h-5 text-orange-400 mr-3"></i>
                                    <h3 class="text-lg font-semibold text-white">Marketing & Advertising</h3>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="marketing-cookies" class="sr-only peer">
                                    <div class="relative w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-500"></div>
                                </label>
                            </div>
                            <p class="text-gray-400 text-sm mb-3">
                                Deliver relevant advertisements and measure campaign effectiveness.
                            </p>
                            <p class="text-gray-500 text-xs">
                                Examples: Google Ads, retargeting, conversion tracking, social media pixels
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- Footer Actions -->
                <div class="flex items-center justify-between p-6 border-t border-slate-700/50">
                    <div class="flex space-x-3">
                        <button id="accept-all" class="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-6 py-2 rounded-lg hover:shadow-lg transition-all duration-300 flex items-center">
                            <i data-lucide="check" class="w-4 h-4 mr-2"></i>
                            Accept All
                        </button>
                        <button id="accept-selected" class="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-2 rounded-lg hover:shadow-lg transition-all duration-300">
                            Save Preferences
                        </button>
                    </div>
                    <button id="reject-all" class="text-gray-400 hover:text-white px-4 py-2 rounded-lg border border-slate-600 hover:border-slate-500 transition-all duration-300">
                        Reject All
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer Placeholder -->
    <div id="footer-placeholder"></div>

    <script>
        // Animated lines background
        function createLinesAnimation() {
            const canvas = document.getElementById('lines-canvas');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            let animationId;
            
            function resizeCanvas() {
                canvas.width = canvas.offsetWidth;
                canvas.height = canvas.offsetHeight;
            }
            
            window.addEventListener('resize', resizeCanvas);
            resizeCanvas();
            
            const lines = [];
            const maxLines = 6;
            
            for (let i = 0; i < maxLines; i++) {
                lines.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    length: Math.random() * 80 + 40,
                    angle: Math.random() * Math.PI * 2,
                    speed: Math.random() * 1.5 + 0.5,
                    opacity: Math.random() * 0.4 + 0.1
                });
            }
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                lines.forEach(line => {
                    ctx.beginPath();
                    ctx.moveTo(line.x, line.y);
                    
                    const endX = line.x + Math.cos(line.angle) * line.length;
                    const endY = line.y + Math.sin(line.angle) * line.length;
                    
                    ctx.lineTo(endX, endY);
                    ctx.strokeStyle = `rgba(245, 158, 11, ${line.opacity})`;
                    ctx.lineWidth = 1;
                    ctx.stroke();
                    
                    line.x += Math.cos(line.angle) * line.speed;
                    line.y += Math.sin(line.angle) * line.speed;
                    
                    if (line.x < -line.length || line.x > canvas.width + line.length ||
                        line.y < -line.length || line.y > canvas.height + line.length) {
                        line.x = Math.random() * canvas.width;
                        line.y = Math.random() * canvas.height;
                        line.angle = Math.random() * Math.PI * 2;
                    }
                });
                
                animationId = requestAnimationFrame(animate);
            }
            
            animate();
            
            return () => {
                if (animationId) {
                    cancelAnimationFrame(animationId);
                }
                window.removeEventListener('resize', resizeCanvas);
            };
        }
        
        // Smooth scroll for anchor links
        document.addEventListener('DOMContentLoaded', () => {
            createLinesAnimation();
            
            // Initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }

            // Smooth scroll for TOC links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Cookie preference center functionality
            const modal = document.getElementById('cookie-modal');
            const openBtn = document.getElementById('cookie-preferences-btn');
            const closeBtn = document.getElementById('close-modal');
            const acceptAllBtn = document.getElementById('accept-all');
            const acceptSelectedBtn = document.getElementById('accept-selected');
            const rejectAllBtn = document.getElementById('reject-all');

            // Open modal
            openBtn.addEventListener('click', function() {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
                // Re-initialize icons for modal content
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            });

            // Close modal
            function closeModal() {
                modal.classList.add('hidden');
                document.body.style.overflow = '';
            }

            closeBtn.addEventListener('click', closeModal);

            // Close modal when clicking backdrop
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // Close modal on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                    closeModal();
                }
            });

            // Accept all cookies
            acceptAllBtn.addEventListener('click', function() {
                document.getElementById('analytics-cookies').checked = true;
                document.getElementById('functionality-cookies').checked = true;
                document.getElementById('marketing-cookies').checked = true;
                saveCookiePreferences();
            });

            // Reject all cookies (except essential)
            rejectAllBtn.addEventListener('click', function() {
                document.getElementById('analytics-cookies').checked = false;
                document.getElementById('functionality-cookies').checked = false;
                document.getElementById('marketing-cookies').checked = false;
                saveCookiePreferences();
            });

            // Save selected preferences
            acceptSelectedBtn.addEventListener('click', function() {
                saveCookiePreferences();
            });

            // Save cookie preferences to localStorage
            function saveCookiePreferences() {
                const preferences = {
                    essential: true, // Always true
                    analytics: document.getElementById('analytics-cookies').checked,
                    functionality: document.getElementById('functionality-cookies').checked,
                    marketing: document.getElementById('marketing-cookies').checked,
                    timestamp: new Date().toISOString()
                };

                localStorage.setItem('cookiePreferences', JSON.stringify(preferences));
                
                // Show success message
                showNotification('Cookie preferences saved successfully!', 'success');
                
                // Close modal
                closeModal();
            }

            // Load saved preferences
            function loadCookiePreferences() {
                const saved = localStorage.getItem('cookiePreferences');
                if (saved) {
                    const preferences = JSON.parse(saved);
                    document.getElementById('analytics-cookies').checked = preferences.analytics;
                    document.getElementById('functionality-cookies').checked = preferences.functionality;
                    document.getElementById('marketing-cookies').checked = preferences.marketing;
                }
            }

            // Show notification
            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-medium transform translate-x-full transition-transform duration-300 ${
                    type === 'success' ? 'bg-green-500' : 'bg-blue-500'
                }`;
                notification.textContent = message;
                
                document.body.appendChild(notification);
                
                // Slide in
                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);
                
                // Slide out and remove
                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 300);
                }, 3000);
            }

            // Load preferences on page load
            loadCookiePreferences();
        });
    </script>
</body>
</html>