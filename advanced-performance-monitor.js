// Google's Advanced Performance Monitoring & Real-Time Optimization
// This implements cutting-edge Web Vitals monitoring and Google's 2025 ranking signals

class AdvancedPerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.initialized = false;
        this.performanceObserver = null;
        this.init();
    }

    init() {
        if (this.initialized) return;
        
        // Monitor Core Web Vitals with Google's 2025 enhanced metrics
        this.monitorCoreWebVitals();
        
        // Track Google's experimental ranking signals
        this.monitorExperimentalSignals();
        
        // Implement real-time optimization
        this.implementRealTimeOptimization();
        
        // Monitor user engagement for Google's behavioral signals
        this.monitorUserEngagement();
        
        this.initialized = true;
    }

    monitorCoreWebVitals() {
        // Enhanced Largest Contentful Paint (LCP) monitoring
        this.observePerformanceEntry('largest-contentful-paint', (entry) => {
            const lcp = entry.startTime;
            this.metrics.lcp = lcp;
            
            // Google 2025: Report LCP with element details
            this.reportMetric('lcp', {
                value: lcp,
                element: entry.element?.tagName || 'unknown',
                url: entry.url || window.location.href,
                timestamp: Date.now(),
                deviceType: this.getDeviceType(),
                connectionType: this.getConnectionType()
            });
            
            // Real-time optimization if LCP is poor
            if (lcp > 2500) {
                this.optimizeLCP();
            }
        });

        // Enhanced Interaction to Next Paint (INP) - Google's 2025 priority
        this.observePerformanceEntry('event', (entry) => {
            if (entry.name === 'pointerdown' || entry.name === 'click') {
                const inp = entry.processingEnd - entry.startTime;
                this.metrics.inp = inp;

                // Google 2025: Enhanced INP tracking with interaction context
                this.reportMetric('inp', {
                    value: inp,
                    eventType: entry.name,
                    target: entry.target?.tagName || 'unknown',
                    targetId: entry.target?.id || '',
                    targetClass: entry.target?.className || '',
                    timestamp: Date.now(),
                    interactionId: entry.interactionId || 0,
                    duration: entry.duration || 0,
                    processingStart: entry.processingStart || 0,
                    processingEnd: entry.processingEnd || 0,
                    presentationTime: entry.startTime + entry.duration,
                    inputDelay: entry.processingStart - entry.startTime,
                    processingTime: entry.processingEnd - entry.processingStart,
                    presentationDelay: entry.startTime + entry.duration - entry.processingEnd
                });

                // Real-time optimization for poor INP with detailed analysis
                if (inp > 200) {
                    this.optimizeINP(entry);
                }

                // Track interaction patterns for AI optimization
                this.trackInteractionPatterns(entry);
            }
        });

        // Enhanced Cumulative Layout Shift (CLS) with element tracking
        this.observePerformanceEntry('layout-shift', (entry) => {
            if (!entry.hadRecentInput) {
                const cls = entry.value;
                this.metrics.cls = (this.metrics.cls || 0) + cls;
                
                this.reportMetric('cls', {
                    value: cls,
                    sources: entry.sources?.map(s => s.node?.tagName) || [],
                    timestamp: Date.now()
                });
            }
        });

        // First Input Delay (FID) enhanced monitoring
        this.observePerformanceEntry('first-input', (entry) => {
            const fid = entry.processingStart - entry.startTime;
            this.metrics.fid = fid;
            
            this.reportMetric('fid', {
                value: fid,
                eventType: entry.name,
                timestamp: Date.now()
            });
        });
    }

    monitorExperimentalSignals() {
        // Google's experimental: Time to Interactive (TTI) precision
        this.calculateTTI();
        
        // Google's experimental: Smooth scrolling performance
        this.monitorScrollPerformance();
        
        // Google's experimental: Animation frame consistency
        this.monitorAnimationPerformance();
        
        // Google's experimental: Memory usage efficiency
        this.monitorMemoryUsage();
    }

    implementRealTimeOptimization() {
        // Dynamic resource loading optimization
        this.optimizeResourceLoading();
        
        // Adaptive image loading based on viewport and connection
        this.implementAdaptiveImageLoading();
        
        // Real-time font optimization
        this.optimizeFontLoading();
        
        // Dynamic script loading prioritization
        this.optimizeScriptLoading();
    }

    monitorUserEngagement() {
        // Google's behavioral signals: Time on page
        let startTime = Date.now();
        let isVisible = true;
        
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                isVisible = false;
                this.reportEngagement('visibility_hidden', Date.now() - startTime);
            } else {
                isVisible = true;
                startTime = Date.now();
            }
        });

        // Scroll depth tracking for engagement
        let maxScroll = 0;
        window.addEventListener('scroll', () => {
            const scrollPercent = Math.round(
                (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
            );
            if (scrollPercent > maxScroll) {
                maxScroll = scrollPercent;
                this.reportEngagement('scroll_depth', scrollPercent);
            }
        });

        // Click tracking for interaction signals
        document.addEventListener('click', (e) => {
            this.reportEngagement('click', {
                element: e.target.tagName,
                timestamp: Date.now(),
                coordinates: { x: e.clientX, y: e.clientY }
            });
        });
    }

    optimizeLCP() {
        // Dynamic optimization for poor LCP
        console.log('🚀 Optimizing LCP performance');
        
        // Preload critical resources
        const criticalImages = document.querySelectorAll('img[loading="eager"]');
        criticalImages.forEach(img => {
            if (!img.complete) {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'image';
                link.href = img.src;
                document.head.appendChild(link);
            }
        });
    }

    optimizeINP(entry) {
        // Google 2025: Advanced INP optimization with interaction analysis
        console.log('⚡ Optimizing INP performance with detailed analysis');

        if (entry) {
            const inputDelay = entry.processingStart - entry.startTime;
            const processingTime = entry.processingEnd - entry.processingStart;
            const presentationDelay = entry.startTime + entry.duration - entry.processingEnd;

            // Optimize based on the bottleneck
            if (inputDelay > 50) {
                this.optimizeInputDelay();
            }

            if (processingTime > 100) {
                this.optimizeProcessingTime(entry);
            }

            if (presentationDelay > 50) {
                this.optimizePresentationDelay();
            }
        }

        // Debounce rapid interactions
        this.implementInteractionDebouncing();

        // Optimize expensive operations
        this.deferNonCriticalOperations();

        // Track interaction patterns for AI optimization
        if (entry) {
            this.trackInteractionPatterns(entry);
        }
    }

    reportMetric(metricName, data) {
        // Send to Google Analytics 4 with enhanced data
        if (typeof gtag !== 'undefined') {
            gtag('event', 'web_vitals', {
                metric_name: metricName,
                metric_value: data.value,
                custom_parameters: {
                    device_type: this.getDeviceType(),
                    connection_type: this.getConnectionType(),
                    page_url: window.location.href,
                    timestamp: Date.now()
                }
            });
        }

        // Send to custom analytics endpoint for advanced analysis
        if (navigator.sendBeacon) {
            navigator.sendBeacon('/analytics/web-vitals', JSON.stringify({
                metric: metricName,
                data: data,
                userAgent: navigator.userAgent,
                url: window.location.href
            }));
        }
    }

    reportEngagement(eventType, data) {
        // Report engagement signals to Google
        if (typeof gtag !== 'undefined') {
            gtag('event', 'engagement', {
                event_type: eventType,
                event_data: data,
                page_url: window.location.href
            });
        }
    }

    observePerformanceEntry(type, callback) {
        if ('PerformanceObserver' in window) {
            try {
                const observer = new PerformanceObserver((list) => {
                    list.getEntries().forEach(callback);
                });
                observer.observe({ entryTypes: [type] });
            } catch (e) {
                console.warn('Performance Observer not supported for type:', type);
            }
        }
    }

    getDeviceType() {
        const userAgent = navigator.userAgent;
        if (/tablet|ipad|playbook|silk/i.test(userAgent)) return 'tablet';
        if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) return 'mobile';
        return 'desktop';
    }

    getConnectionType() {
        return navigator.connection?.effectiveType || 'unknown';
    }

    calculateTTI() {
        // Advanced TTI calculation using Google's latest methodology
        window.addEventListener('load', () => {
            setTimeout(() => {
                const tti = performance.now();
                this.metrics.tti = tti;
                this.reportMetric('tti', { value: tti, timestamp: Date.now() });
            }, 0);
        });
    }

    monitorScrollPerformance() {
        let lastScrollTime = 0;
        let scrollEvents = 0;
        
        window.addEventListener('scroll', () => {
            const now = performance.now();
            const deltaTime = now - lastScrollTime;
            
            if (deltaTime > 16.67) { // 60fps threshold
                scrollEvents++;
            }
            
            lastScrollTime = now;
            
            // Report scroll performance every 100 events
            if (scrollEvents % 100 === 0) {
                this.reportMetric('scroll_performance', {
                    jank_events: scrollEvents,
                    timestamp: Date.now()
                });
            }
        }, { passive: true });
    }

    monitorAnimationPerformance() {
        let frameCount = 0;
        let lastTime = performance.now();
        
        const measureFrameRate = () => {
            const now = performance.now();
            frameCount++;
            
            if (now - lastTime >= 1000) {
                const fps = Math.round((frameCount * 1000) / (now - lastTime));
                this.reportMetric('animation_fps', {
                    fps: fps,
                    timestamp: Date.now()
                });
                
                frameCount = 0;
                lastTime = now;
            }
            
            requestAnimationFrame(measureFrameRate);
        };
        
        requestAnimationFrame(measureFrameRate);
    }

    monitorMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                this.reportMetric('memory_usage', {
                    used: memory.usedJSHeapSize,
                    total: memory.totalJSHeapSize,
                    limit: memory.jsHeapSizeLimit,
                    timestamp: Date.now()
                });
            }, 30000); // Every 30 seconds
        }
    }

    optimizeResourceLoading() {
        // Dynamic resource prioritization based on user behavior
        const images = document.querySelectorAll('img[loading="lazy"]');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.loading = 'eager';
                    observer.unobserve(img);
                }
            });
        }, { rootMargin: '50px' });
        
        images.forEach(img => observer.observe(img));
    }

    implementAdaptiveImageLoading() {
        // Adaptive image quality based on connection speed
        const connection = navigator.connection;
        if (connection) {
            const quality = connection.effectiveType === '4g' ? 'high' : 'medium';
            document.documentElement.setAttribute('data-connection-quality', quality);
        }
    }

    optimizeFontLoading() {
        // Dynamic font loading optimization
        document.fonts.ready.then(() => {
            this.reportMetric('fonts_loaded', {
                count: document.fonts.size,
                timestamp: Date.now()
            });
        });
    }

    optimizeScriptLoading() {
        // Prioritize critical scripts based on user interaction
        const scripts = document.querySelectorAll('script[data-priority="low"]');
        
        const loadLowPriorityScripts = () => {
            scripts.forEach(script => {
                if (!script.hasAttribute('data-loaded')) {
                    script.src = script.getAttribute('data-src');
                    script.setAttribute('data-loaded', 'true');
                }
            });
        };
        
        // Load after user interaction or 3 seconds
        ['click', 'scroll', 'keydown'].forEach(event => {
            document.addEventListener(event, loadLowPriorityScripts, { once: true });
        });
        
        setTimeout(loadLowPriorityScripts, 3000);
    }

    implementInteractionDebouncing() {
        // Debounce rapid interactions to improve INP
        let interactionTimeout;
        
        document.addEventListener('click', (e) => {
            if (interactionTimeout) {
                clearTimeout(interactionTimeout);
            }
            
            interactionTimeout = setTimeout(() => {
                // Process interaction after debounce
                this.processInteraction(e);
            }, 50);
        });
    }

    deferNonCriticalOperations() {
        // Defer non-critical operations to improve responsiveness
        if ('scheduler' in window && 'postTask' in scheduler) {
            scheduler.postTask(() => {
                // Non-critical operations here
                this.performNonCriticalTasks();
            }, { priority: 'background' });
        } else {
            setTimeout(() => {
                this.performNonCriticalTasks();
            }, 0);
        }
    }

    processInteraction(event) {
        // Process user interactions efficiently
        console.log('Processing interaction:', event.type);
    }

    performNonCriticalTasks() {
        // Background tasks that don't affect user experience
        console.log('Performing non-critical background tasks');
    }

    optimizeInputDelay() {
        // Reduce main thread blocking
        if (window.requestIdleCallback) {
            window.requestIdleCallback(() => {
                // Move non-critical work to idle time
                this.deferNonCriticalWork();
            });
        }
    }

    optimizeProcessingTime(entry) {
        // Break up long tasks
        const target = entry.target;
        if (target && target.addEventListener) {
            // Replace synchronous event handlers with async ones
            this.asyncifyEventHandlers(target);
        }
    }

    optimizePresentationDelay() {
        // Reduce layout thrashing
        document.documentElement.style.containIntrinsicSize = 'auto';

        // Enable content-visibility for off-screen content
        document.querySelectorAll('.below-fold').forEach(element => {
            element.style.contentVisibility = 'auto';
        });
    }

    deferNonCriticalWork() {
        // Move non-critical operations to idle time
        const nonCriticalTasks = [
            () => this.cleanupOldMetrics(),
            () => this.optimizeImageLoading(),
            () => this.preloadNextPageResources()
        ];

        nonCriticalTasks.forEach(task => {
            if (window.requestIdleCallback) {
                window.requestIdleCallback(task);
            } else {
                setTimeout(task, 0);
            }
        });
    }

    asyncifyEventHandlers(element) {
        // Convert synchronous event handlers to async
        const events = ['click', 'input', 'change', 'submit'];

        events.forEach(eventType => {
            const handlers = element.getEventListeners?.(eventType) || [];
            handlers.forEach(handler => {
                if (handler.listener && !handler.listener.async) {
                    // Wrap handler in async function
                    const asyncHandler = async (event) => {
                        await new Promise(resolve => {
                            handler.listener(event);
                            resolve();
                        });
                    };

                    element.removeEventListener(eventType, handler.listener);
                    element.addEventListener(eventType, asyncHandler);
                }
            });
        });
    }
}

// Initialize the advanced performance monitor
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new AdvancedPerformanceMonitor();
    });
} else {
    new AdvancedPerformanceMonitor();
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdvancedPerformanceMonitor;
}