/**
 * X-ZoneServers Advanced Image SEO Optimization
 * Google 2025: Image optimization for Core Web Vitals and search rankings
 */

class ImageSEOOptimizer {
    constructor() {
        this.imageCache = new Map();
        this.lazyLoadObserver = null;
        this.init();
    }

    init() {
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupImageOptimization());
        } else {
            this.setupImageOptimization();
        }
    }

    setupImageOptimization() {
        this.addImageSchemaMarkup();
        this.optimizeBackgroundImages();
        this.setupLazyLoading();
        this.addImagePreloading();
        this.setupImageErrorHandling();
    }

    // Add structured data for images used in Open Graph and Twitter Cards
    addImageSchemaMarkup() {
        const imageSchema = {
            "@context": "https://schema.org",
            "@type": "ImageObject",
            "contentUrl": "https://x-zoneservers.com/images/og-homepage.jpg",
            "name": "X-ZoneServers Enterprise Hosting Solutions",
            "description": "High-performance dedicated servers and VPS hosting infrastructure",
            "width": "1200",
            "height": "630",
            "encodingFormat": "image/jpeg",
            "author": {
                "@type": "Organization",
                "name": "X-ZoneServers"
            },
            "copyrightHolder": {
                "@type": "Organization",
                "name": "X-ZoneServers"
            },
            "license": "https://x-zoneservers.com/terms-and-conditions.html",
            "acquireLicensePage": "https://x-zoneservers.com/about.html"
        };

        // Add schema to page
        const script = document.createElement('script');
        script.type = 'application/ld+json';
        script.textContent = JSON.stringify(imageSchema);
        document.head.appendChild(script);
    }

    // Optimize background images for better performance
    optimizeBackgroundImages() {
        const elementsWithBg = document.querySelectorAll('[style*="background-image"], .bg-gradient, .hero-bg');
        
        elementsWithBg.forEach(element => {
            // Add loading attribute for better performance
            element.setAttribute('loading', 'lazy');
            
            // Add image alt text equivalent via aria-label
            if (!element.getAttribute('aria-label')) {
                element.setAttribute('aria-label', 'X-ZoneServers hosting infrastructure background');
            }
            
            // Optimize for Core Web Vitals
            element.style.contentVisibility = 'auto';
            element.style.containIntrinsicSize = '1200px 600px';
        });
    }

    // Setup lazy loading for better performance
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            this.lazyLoadObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        this.lazyLoadObserver.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            // Observe elements that might have images
            document.querySelectorAll('.lazy-bg, [data-bg]').forEach(el => {
                this.lazyLoadObserver.observe(el);
            });
        }
    }

    loadImage(element) {
        const bgImage = element.dataset.bg;
        if (bgImage) {
            element.style.backgroundImage = `url(${bgImage})`;
            element.classList.add('loaded');
        }
    }

    // Add preloading for critical images
    addImagePreloading() {
        const criticalImages = [
            'https://x-zoneservers.com/images/og-homepage.jpg',
            'https://x-zoneservers.com/images/twitter-homepage.jpg',
            'https://x-zoneservers.com/images/hero-background.jpg'
        ];

        criticalImages.forEach(imageUrl => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = imageUrl;
            link.fetchpriority = 'high';
            document.head.appendChild(link);
        });
    }

    // Setup error handling for images
    setupImageErrorHandling() {
        // Handle background image errors
        document.addEventListener('error', (e) => {
            if (e.target.tagName === 'IMG') {
                this.handleImageError(e.target);
            }
        }, true);
    }

    handleImageError(img) {
        // Fallback to placeholder or retry
        img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwMCIgaGVpZ2h0PSI2MDAiIHZpZXdCb3g9IjAgMCAxMjAwIDYwMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjEyMDAiIGhlaWdodD0iNjAwIiBmaWxsPSIjMUUyOTNCIi8+Cjx0ZXh0IHg9IjYwMCIgeT0iMzAwIiBmaWxsPSIjNjM3NEE4IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkludGVyLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjI0Ij5YLVpvbmVTZXJ2ZXJzPC90ZXh0Pgo8L3N2Zz4K';
        img.alt = 'X-ZoneServers - Image temporarily unavailable';
    }

    // Generate image sitemap data
    generateImageSitemapData() {
        return {
            images: [
                {
                    loc: 'https://x-zoneservers.com/images/og-homepage.jpg',
                    title: 'X-ZoneServers Enterprise Hosting Solutions',
                    caption: 'High-performance dedicated servers and VPS hosting infrastructure with global network coverage'
                },
                {
                    loc: 'https://x-zoneservers.com/images/dedicated-servers.jpg',
                    title: 'Dedicated Streaming Servers',
                    caption: 'Powerful bare metal servers with dual Xeon processors and up to 100Gbps bandwidth'
                },
                {
                    loc: 'https://x-zoneservers.com/images/vps-hosting-premium.jpg',
                    title: 'Premium VPS Hosting Plans',
                    caption: 'KVM VPS hosting with 10Gbps bandwidth, SSD storage, and instant deployment'
                },
                {
                    loc: 'https://x-zoneservers.com/images/network-infrastructure.jpg',
                    title: 'Global Network Infrastructure',
                    caption: 'AI-optimized global network with Tier 1 & Tier 2 peers and premium connectivity'
                },
                {
                    loc: 'https://x-zoneservers.com/images/game-server-hosting.jpg',
                    title: 'Game Server Hosting',
                    caption: 'Ultra-low latency game servers optimized for Minecraft, CS2, Rust, and more'
                }
            ]
        };
    }

    // Add WebP support detection and optimization
    supportsWebP() {
        return new Promise((resolve) => {
            const webP = new Image();
            webP.onload = webP.onerror = () => resolve(webP.height === 2);
            webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
        });
    }

    // Optimize image loading based on connection speed
    async optimizeForConnection() {
        if ('connection' in navigator) {
            const connection = navigator.connection;
            const slowConnection = connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g';
            
            if (slowConnection) {
                // Reduce image quality for slow connections
                document.documentElement.classList.add('slow-connection');
            }
        }
    }
}

// Initialize image SEO optimization
const imageSEO = new ImageSEOOptimizer();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ImageSEOOptimizer;
}
