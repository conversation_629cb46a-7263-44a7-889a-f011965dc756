/**
 * AI-Powered Personalization Schema for Google 2025
 * Dynamic content optimization with user intent prediction
 */

class AIPersonalizationOptimizer {
    constructor() {
        this.userProfile = {};
        this.intentSignals = [];
        this.personalizationData = new Map();
        this.init();
    }

    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupPersonalization());
        } else {
            this.setupPersonalization();
        }
    }

    setupPersonalization() {
        this.analyzeUserIntent();
        this.addPersonalizationSchema();
        this.implementDynamicContent();
        this.trackUserBehavior();
        this.optimizeForUserSegments();
    }

    analyzeUserIntent() {
        // Analyze user behavior patterns for intent prediction
        const referrer = document.referrer;
        const userAgent = navigator.userAgent;
        const timeOfDay = new Date().getHours();
        const dayOfWeek = new Date().getDay();
        
        // Determine user segment based on behavior
        let userSegment = 'general';
        
        if (referrer.includes('google') && referrer.includes('enterprise')) {
            userSegment = 'enterprise';
        } else if (referrer.includes('gaming') || referrer.includes('game')) {
            userSegment = 'gaming';
        } else if (referrer.includes('developer') || referrer.includes('dev')) {
            userSegment = 'developer';
        } else if (timeOfDay >= 9 && timeOfDay <= 17 && dayOfWeek >= 1 && dayOfWeek <= 5) {
            userSegment = 'business';
        }
        
        this.userProfile = {
            segment: userSegment,
            timeOfDay,
            dayOfWeek,
            referrer,
            deviceType: this.getDeviceType(),
            connectionSpeed: this.getConnectionSpeed()
        };
    }

    addPersonalizationSchema() {
        const personalizationSchema = {
            "@context": "https://schema.org",
            "@type": "WebPage",
            "audience": this.getAudienceSchema(),
            "usageInfo": this.getUsageInfoSchema(),
            "potentialAction": this.getPersonalizedActions(),
            "mainEntity": {
                "@type": "Service",
                "name": "Personalized Hosting Solutions",
                "description": this.getPersonalizedDescription(),
                "offers": this.getPersonalizedOffers()
            }
        };

        const script = document.createElement('script');
        script.type = 'application/ld+json';
        script.textContent = JSON.stringify(personalizationSchema);
        document.head.appendChild(script);
    }

    getAudienceSchema() {
        const audiences = {
            enterprise: {
                "@type": "Audience",
                "audienceType": "Enterprise Businesses",
                "geographicArea": "Global",
                "requiredMinAge": 25,
                "requiredMaxAge": 65,
                "suggestedGender": "Any",
                "audienceSize": "Large Organizations"
            },
            gaming: {
                "@type": "Audience", 
                "audienceType": "Gaming Community",
                "geographicArea": "Global",
                "requiredMinAge": 16,
                "requiredMaxAge": 45,
                "interests": ["Gaming", "Esports", "Game Development"]
            },
            developer: {
                "@type": "Audience",
                "audienceType": "Software Developers",
                "geographicArea": "Global", 
                "requiredMinAge": 20,
                "requiredMaxAge": 55,
                "interests": ["Programming", "Cloud Computing", "DevOps"]
            }
        };

        return audiences[this.userProfile.segment] || audiences.enterprise;
    }

    getUsageInfoSchema() {
        return {
            "@type": "CreativeWork",
            "usageInfo": `Optimized for ${this.userProfile.segment} users on ${this.userProfile.deviceType} devices`,
            "accessibilityFeature": ["highContrast", "largePrint", "readingOrder"],
            "accessibilityHazard": "none",
            "accessibilityAPI": ["ARIA"]
        };
    }

    getPersonalizedActions() {
        const actions = {
            enterprise: [
                {
                    "@type": "ViewAction",
                    "name": "View Enterprise Solutions",
                    "target": "https://x-zoneservers.com/dedicated.html"
                },
                {
                    "@type": "ContactAction",
                    "name": "Request Enterprise Quote",
                    "target": "https://x-zoneservers.com/contact"
                }
            ],
            gaming: [
                {
                    "@type": "ViewAction",
                    "name": "Explore Game Hosting",
                    "target": "https://x-zoneservers.com/game-hosting.html"
                },
                {
                    "@type": "PlayAction",
                    "name": "Try Game Server",
                    "target": "https://x-zoneservers.com/trial"
                }
            ],
            developer: [
                {
                    "@type": "ViewAction",
                    "name": "Developer VPS Plans",
                    "target": "https://x-zoneservers.com/streaming-vps.html"
                },
                {
                    "@type": "CreateAction",
                    "name": "Deploy Application",
                    "target": "https://x-zoneservers.com/deploy"
                }
            ]
        };

        return actions[this.userProfile.segment] || actions.enterprise;
    }

    getPersonalizedDescription() {
        const descriptions = {
            enterprise: "Enterprise-grade hosting solutions with 99.9% uptime SLA, dedicated support, and scalable infrastructure for mission-critical applications.",
            gaming: "Ultra-low latency game servers with DDoS protection, instant deployment, and optimized performance for competitive gaming and streaming.",
            developer: "Developer-friendly VPS hosting with full root access, multiple OS options, and seamless integration with popular development tools.",
            business: "Reliable business hosting solutions with professional support, security features, and cost-effective pricing for growing companies."
        };

        return descriptions[this.userProfile.segment] || descriptions.enterprise;
    }

    getPersonalizedOffers() {
        const offers = {
            enterprise: [
                {
                    "@type": "Offer",
                    "name": "Enterprise Dedicated Server",
                    "price": "199",
                    "priceCurrency": "EUR",
                    "description": "High-performance dedicated server with enterprise support"
                }
            ],
            gaming: [
                {
                    "@type": "Offer",
                    "name": "Gaming Server Hosting",
                    "price": "5.99",
                    "priceCurrency": "EUR", 
                    "description": "Ultra-low latency game servers with instant setup"
                }
            ],
            developer: [
                {
                    "@type": "Offer",
                    "name": "Developer VPS",
                    "price": "9.50",
                    "priceCurrency": "EUR",
                    "description": "Full-featured VPS with developer tools and APIs"
                }
            ]
        };

        return offers[this.userProfile.segment] || offers.enterprise;
    }

    implementDynamicContent() {
        // Add personalized meta tags
        const personalizedMeta = [
            { name: 'user-segment', content: this.userProfile.segment },
            { name: 'personalization-level', content: 'high' },
            { name: 'content-optimization', content: 'ai-powered' },
            { name: 'user-intent', content: this.predictUserIntent() }
        ];

        personalizedMeta.forEach(meta => {
            const metaTag = document.createElement('meta');
            metaTag.name = meta.name;
            metaTag.content = meta.content;
            document.head.appendChild(metaTag);
        });

        // Personalize page title and description
        this.personalizePageElements();
    }

    predictUserIntent() {
        const intents = {
            enterprise: 'evaluate-enterprise-hosting',
            gaming: 'setup-game-server',
            developer: 'deploy-application',
            business: 'upgrade-hosting-plan'
        };

        return intents[this.userProfile.segment] || 'explore-hosting-options';
    }

    personalizePageElements() {
        // Personalize based on user segment
        const personalizations = {
            enterprise: {
                titleSuffix: ' - Enterprise Solutions',
                ctaText: 'Get Enterprise Quote',
                priority: 'reliability and support'
            },
            gaming: {
                titleSuffix: ' - Gaming Servers',
                ctaText: 'Start Gaming Server',
                priority: 'low latency and performance'
            },
            developer: {
                titleSuffix: ' - Developer Hosting',
                ctaText: 'Deploy Now',
                priority: 'flexibility and control'
            }
        };

        const config = personalizations[this.userProfile.segment];
        if (config) {
            // Update page title
            document.title += config.titleSuffix;
            
            // Update CTA buttons
            document.querySelectorAll('.cta-button').forEach(button => {
                button.textContent = config.ctaText;
            });
        }
    }

    trackUserBehavior() {
        // Track user interactions for better personalization
        const events = ['click', 'scroll', 'hover', 'focus'];
        
        events.forEach(eventType => {
            document.addEventListener(eventType, (event) => {
                this.recordInteraction(eventType, event.target);
            });
        });
    }

    recordInteraction(type, target) {
        const interaction = {
            type,
            target: target.tagName,
            targetId: target.id,
            targetClass: target.className,
            timestamp: Date.now(),
            userSegment: this.userProfile.segment
        };

        this.intentSignals.push(interaction);
        
        // Keep only recent interactions
        if (this.intentSignals.length > 50) {
            this.intentSignals.shift();
        }

        // Update personalization based on behavior
        this.updatePersonalization();
    }

    updatePersonalization() {
        // Analyze recent interactions to refine personalization
        const recentInteractions = this.intentSignals.slice(-10);
        const interactionPatterns = {};

        recentInteractions.forEach(interaction => {
            const key = `${interaction.type}-${interaction.target}`;
            interactionPatterns[key] = (interactionPatterns[key] || 0) + 1;
        });

        // Adjust user segment based on behavior
        if (interactionPatterns['click-BUTTON'] > 3) {
            this.userProfile.engagementLevel = 'high';
        }

        if (interactionPatterns['hover-A'] > 5) {
            this.userProfile.browsingBehavior = 'exploratory';
        }
    }

    optimizeForUserSegments() {
        // Add segment-specific optimizations
        const optimizations = {
            enterprise: () => this.optimizeForEnterprise(),
            gaming: () => this.optimizeForGaming(),
            developer: () => this.optimizeForDevelopers()
        };

        const optimizer = optimizations[this.userProfile.segment];
        if (optimizer) {
            optimizer();
        }
    }

    optimizeForEnterprise() {
        // Enterprise-specific optimizations
        document.body.classList.add('enterprise-optimized');
        
        // Preload enterprise-related resources
        this.preloadResources([
            '/dedicated.html',
            '/enterprise-features.js',
            '/compliance-info.html'
        ]);
    }

    optimizeForGaming() {
        // Gaming-specific optimizations
        document.body.classList.add('gaming-optimized');
        
        // Preload gaming-related resources
        this.preloadResources([
            '/game-hosting.html',
            '/server-locations.js',
            '/latency-test.html'
        ]);
    }

    optimizeForDevelopers() {
        // Developer-specific optimizations
        document.body.classList.add('developer-optimized');
        
        // Preload developer-related resources
        this.preloadResources([
            '/streaming-vps.html',
            '/api-documentation.js',
            '/developer-tools.html'
        ]);
    }

    preloadResources(resources) {
        resources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = resource;
            document.head.appendChild(link);
        });
    }

    getDeviceType() {
        const width = window.innerWidth;
        if (width < 768) return 'mobile';
        if (width < 1024) return 'tablet';
        return 'desktop';
    }

    getConnectionSpeed() {
        if (navigator.connection) {
            return navigator.connection.effectiveType || 'unknown';
        }
        return 'unknown';
    }
}

// Initialize AI personalization
const aiPersonalization = new AIPersonalizationOptimizer();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AIPersonalizationOptimizer;
}
